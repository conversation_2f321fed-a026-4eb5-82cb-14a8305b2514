<template>
  <el-dialog :title="dialog.title" v-model="dialog.show" width="600px" class="bill-edit-dialog">
    <el-form :model="billModel" :rules="rules" ref="formRef" label-width="100px" class="bill-edit-form">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="billModel.phone" maxlength="11" placeholder="请输入手机号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="支付金额" prop="amount">
            <el-input-number v-model="billModel.amount" :min="0" :precision="2" 
              style="width: 100%;" placeholder="请输入支付金额" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="支付类型" prop="payType">
            <el-select v-model="billModel.payType" placeholder="请选择支付类型" style="width: 100%;">
              <el-option label="微信支付" value="wechat" />
              <el-option label="支付宝" value="alipay" />
              <el-option label="银行卡" value="bank_card" />
              <el-option label="现金" value="cash" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="billModel.status" placeholder="请选择状态" style="width: 100%;">
              <el-option label="待支付" value="pending" />
              <el-option label="已支付" value="paid" />
              <el-option label="已取消" value="cancelled" />
              <el-option label="已退款" value="refunded" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="支付单号" prop="payNo">
            <el-input v-model="billModel.payNo" maxlength="50" placeholder="请输入支付单号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="支付时间" prop="payTime">
            <el-date-picker v-model="billModel.payTime" type="datetime" placeholder="请选择支付时间" 
              style="width: 100%;" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="楼房" prop="buildingId">
            <el-select v-model="billModel.buildingId" placeholder="请选择楼房" 
              style="width: 100%;" filterable>
              <el-option 
                v-for="item in buildingList" 
                :key="item.id" 
                :label="item.buildingNumber" 
                :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="用户openid" prop="openid">
            <el-input v-model="billModel.openid" maxlength="100" placeholder="请输入用户openid" clearable />
            <div class="form-tip">业主不是app用户时，请通知业主注册缴费</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialog.show = false">取消</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addPropertyPaymentBill, editPropertyPaymentBill } from '@/api/property/paymentItems'
import { listPropertyPaymentObject } from '@/api/property/paymentItems'
import { listCommunity } from '@/api/community/community'
import mitt from '@/utils/mitt'
import { listCommunityBuilding } from '@/api/community/communityBuilding'
export default {
  name: 'billEdit',
  data() {
    return {
      billModel: {
        id: undefined,
        paymentObjectId: null,
        unitPrice: null,
        unit: '',
        quantity: null,
        status: '',
        communityId: null,
        payNo: '',
        refundNo: '',
        payTime: '',
        refundTime: '',
        payType: '',
        billDate: '',
        paymentDetailId: null,
        lastQuantity: null,
        payAmount: null,
        discountAmount: null,
        totalAmount: null
      },
      buildingList: [],
      dialog: {
        show: false,
        title: ''
      },
      rules: {
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        amount: [
          { required: true, message: '请输入支付金额', trigger: 'blur' }
        ],
        payType: [
          { required: true, message: '请选择支付类型', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    loadBuildingList() {
      listCommunityBuilding({ pageNum: 1, pageSize: 1000 }).then(res => {
        this.buildingList = res.data.data.list || []
      })
    },
    
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        const api = this.billModel.id ? editPropertyPaymentBill : addPropertyPaymentBill
        api(this.billModel).then(() => {
          this.$emit('search')
          this.dialog.show = false
          this.$message.success('操作成功')
        })
      })
    }
  },
  mounted() {
    this.loadBuildingList()
    
    mitt.on('openBillEdit', (data) => {
      this.billModel = { ...data }
      this.dialog.show = true
      this.dialog.title = '编辑物业账单'
    })

    mitt.on('openBillAdd', () => {
      this.billModel = {}
      this.dialog.show = true
      this.dialog.title = '新增物业账单'
    })
  },
  beforeDestroy() {
    mitt.off('openBillEdit')
    mitt.off('openBillAdd')
  }
}
</script>

<style scoped>
.bill-edit-dialog>>>.el-dialog__body {
  padding-top: 10px;
  padding-bottom: 0;
}

.bill-edit-form {
  padding: 0 10px;
}

.dialog-footer {
  padding: 10px 24px 18px 0;
  text-align: right;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style> 