<template>
  <div class="bill-list-container">
    <div class="bill-list-content">
      <bill-edit @search="search" ref="editDialog" />
      <div class="card card--search search-flex">
        <el-select v-model="searchModel.status" placeholder="账单状态" clearable style="width: 200px; margin-right: 16px;">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
        <el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
        <el-button type="primary" @click="add">添加</el-button>
      </div>
      
      <!-- 统计卡片 -->
      <div class="stats-cards">
        <div class="stat-card">
          <div class="stat-title">总收入</div>
          <div class="stat-value">¥{{ totalIncome }}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">待支付</div>
          <div class="stat-value pending">{{ pendingCount }}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">已支付</div>
          <div class="stat-value paid">{{ paidCount }}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">已退款</div>
          <div class="stat-value refunded">{{ refundedCount }}</div>
        </div>
      </div>
      
      <div class="card card--table">
        <div class="table-col">
          <el-table :data="billList" row-key="id" style="width: 100%; height: 100%;" class="data-table">
            <el-table-column prop="id" label="ID" width="80" align="center"/>
            <el-table-column prop="paymentObjectId" label="缴费对象ID" width="120" align="center"/>
            <el-table-column prop="paymentDetailId" label="缴费明细ID" width="120" align="center"/>
            <el-table-column prop="unitPrice" label="单价" align="center">
              <template #default="scope">
                <span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.unitPrice }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="unit" label="单位" width="80" align="center"/>
            <el-table-column prop="quantity" label="数量" width="80" align="center"/>
            <el-table-column prop="lastQuantity" label="上次数量" width="100" align="center"/>
            <el-table-column prop="totalAmount" label="总金额" align="center">
              <template #default="scope">
                <span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.totalAmount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" align="center">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="billDate" label="账单日期" align="center"/>
            <el-table-column label="操作" width="180" fixed="right">
              <template #default="scope">
                <el-button type="text" size="mini" @click="edit(scope.row.id)">编辑</el-button>
                <el-button type="text" size="mini" @click="deleted(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-col">
          <el-pagination background layout="prev, pager, next" @current-change="currentChange" 
            :total="total" :page-size="searchModel.pageSize" :current-page="searchModel.pageNum" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { listPropertyPaymentBills, deletePropertyPaymentBill, getPropertyPaymentBill } from '@/api/property/paymentItems'
import { listDictByNameEn } from '@/api/system/dict'
import mitt from '@/utils/mitt'
import billEdit from '@/components/property/billEdit.vue'

export default {
  components: { billEdit },
  data() {
    return {
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        status: ''
      },
      billList: [],
      total: 0,
      statusOptions: [],
      // 统计数据
      totalIncome: 0,
      pendingCount: 0,
      paidCount: 0,
      refundedCount: 0
    }
  },
  methods: {
    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        // 加载账单状态字典
        const statusRes = await listDictByNameEn('property_payment_bill_status')
        this.statusOptions = (statusRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 如果字典为空，使用默认选项
        if (this.statusOptions.length === 0) {
          this.statusOptions = [
            { value: 'pending', label: '待支付' },
            { value: 'paid', label: '已支付' },
            { value: 'cancelled', label: '已取消' },
            { value: 'refunded', label: '已退款' }
          ]
        }
      } catch (err) {
        console.error('加载字典数据失败:', err)
        // 使用默认状态选项
        this.statusOptions = [
          { value: 'pending', label: '待支付' },
          { value: 'paid', label: '已支付' },
          { value: 'cancelled', label: '已取消' },
          { value: 'refunded', label: '已退款' }
        ]
      }
    },

    search() {
      listPropertyPaymentBills(this.searchModel).then(res => {
        this.billList = res.data.data.list || []
        this.total = res.data.data.total || 0
        this.calculateStats()
      }).catch(err => {
        this.$message.error(err.data?.errorMessage || '查询失败')
      })
    },
    calculateStats() {
      this.totalIncome = 0
      this.pendingCount = 0
      this.paidCount = 0
      this.refundedCount = 0

      this.billList.forEach(bill => {
        if (bill.status === 'paid') {
          this.totalIncome += (bill.totalAmount || 0)
          this.paidCount++
        } else if (bill.status === 'pending') {
          this.pendingCount++
        } else if (bill.status === 'refunded') {
          this.refundedCount++
        }
      })
    },
    add() {
      mitt.emit('openBillAdd')
    },
    edit(id) {
      getPropertyPaymentBill(id).then(res => {
        mitt.emit('openBillEdit', res.data.data)
      }).catch(err => {
        this.$message.error(err.data?.errorMessage || '获取数据失败')
      })
    },
    deleted(id) {
      this.$confirm('删除物业账单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePropertyPaymentBill(id).then(() => {
          this.search()
          this.$message.success('操作成功')
        }).catch(err => {
          this.$message.error(err.data?.errorMessage || '删除失败')
        })
      }).catch(() => {})
    },
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },
    getStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'paid': 'success',
        'cancelled': 'info',
        'refunded': 'danger'
      }
      return statusMap[status] || 'info'
    },
    getStatusText(status) {
      const option = this.statusOptions.find(item => item.value === status)
      return option ? option.label : status
    },
    getPayTypeText(payType) {
      const payTypeMap = {
        'wechat': '微信支付',
        'alipay': '支付宝',
        'bank_card': '银行卡',
        'cash': '现金'
      }
      return payTypeMap[payType] || payType
    }
  },
  async created() {
    await this.initDictData()
    this.search()
  }
}
</script>

<style scoped>
::v-deep .--el-table-index {
  z-index: 9999 !important;
}

.bill-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.bill-list-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}
.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}
.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
.dark-theme .card {
  background-color: var(--card-background);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}
.search-flex {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  gap: 16px;
  margin: 16px 0;
}

.stat-card {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card:nth-child(2) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card:nth-child(3) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card:nth-child(4) {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-title {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
}

.stat-value.pending {
  color: #f56c6c;
}

.stat-value.paid {
  color: #67c23a;
}

.stat-value.refunded {
  color: #909399;
}
</style> 